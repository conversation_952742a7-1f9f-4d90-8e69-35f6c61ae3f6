#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
慢性肾病状态预测分析 - 测试脚本
用于验证数据加载和基本分析功能
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings

# 设置中文字体和图形样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.figsize'] = (12, 8)
sns.set_style("whitegrid")
warnings.filterwarnings('ignore')

def test_data_loading():
    """测试数据加载功能"""
    print("=== 测试数据加载 ===")
    
    data_files = {
        'data_clean': 'data_clean.csv',
        'kidney_clean': 'kidney_clean.csv',
        'excel': '慢性肾病数据.xlsx'
    }
    
    loaded_data = {}
    
    for name, file_path in data_files.items():
        try:
            if name == 'excel':
                df = pd.read_excel(file_path)
            else:
                df = pd.read_csv(file_path)
            
            print(f"✓ 成功加载 {name}: {df.shape}")
            print(f"  列名: {list(df.columns)}")
            
            # 检查目标变量
            target_vars = ['stage', 'rate']
            for target in target_vars:
                if target in df.columns:
                    print(f"  {target} 唯一值: {sorted(df[target].unique())}")
            
            loaded_data[name] = df
            print()
            
        except FileNotFoundError:
            print(f"✗ 未找到文件: {file_path}")
        except Exception as e:
            print(f"✗ 加载 {name} 时出错: {e}")
    
    return loaded_data

def test_data_visualization(df, data_name):
    """测试数据可视化功能"""
    print(f"=== 测试 {data_name} 数据可视化 ===")
    
    try:
        # 检查目标变量并绘制分布图
        target_vars = ['stage', 'rate']
        
        for target in target_vars:
            if target in df.columns:
                plt.figure(figsize=(10, 6))
                
                # 创建中文标签
                if target == 'stage':
                    stage_mapping = {0: 'CKD1期', 1: 'CKD2期', 2: 'CKD3期', 3: 'CKD4期', 4: 'CKD5期'}
                    counts = df[target].value_counts().sort_index()
                    labels = [stage_mapping.get(idx, f'未知{idx}') for idx in counts.index]
                    title = 'CKD分期分布'
                else:
                    rate_mapping = {0: '低危', 1: '中危', 2: '高危', 3: '极高危'}
                    counts = df[target].value_counts().sort_index()
                    labels = [rate_mapping.get(idx, f'未知{idx}') for idx in counts.index]
                    title = 'CKD风险分层分布'
                
                # 绘制饼图
                plt.subplot(1, 2, 1)
                plt.pie(counts.values, labels=labels, autopct='%1.1f%%', startangle=90)
                plt.title(f'{title}（饼图）')
                
                # 绘制柱状图
                plt.subplot(1, 2, 2)
                bars = plt.bar(labels, counts.values, color=plt.cm.Set3.colors[:len(counts)])
                plt.title(f'{title}（柱状图）')
                plt.xlabel(target)
                plt.ylabel('患者数量')
                plt.xticks(rotation=45)
                
                # 添加数值标签
                for bar, count in zip(bars, counts.values):
                    plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                           str(count), ha='center', va='bottom')
                
                plt.tight_layout()
                plt.show()
                
                print(f"✓ {title}可视化完成")
        
        # 绘制数值型变量分布
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) > 0:
            print(f"数值型变量: {list(numeric_cols)}")
            
            # 选择前4个数值型变量进行可视化
            cols_to_plot = numeric_cols[:4]
            if len(cols_to_plot) > 0:
                fig, axes = plt.subplots(2, 2, figsize=(15, 10))
                axes = axes.ravel()
                
                for i, col in enumerate(cols_to_plot):
                    data = df[col].dropna()
                    axes[i].hist(data, bins=30, alpha=0.7, edgecolor='black')
                    axes[i].set_title(f'{col} 分布')
                    axes[i].set_xlabel(col)
                    axes[i].set_ylabel('频数')
                
                # 隐藏多余的子图
                for i in range(len(cols_to_plot), 4):
                    axes[i].set_visible(False)
                
                plt.tight_layout()
                plt.show()
                print("✓ 数值型变量分布可视化完成")
        
    except Exception as e:
        print(f"✗ 可视化时出错: {e}")

def test_basic_analysis(df, data_name):
    """测试基本数据分析"""
    print(f"=== 测试 {data_name} 基本分析 ===")
    
    try:
        print(f"数据形状: {df.shape}")
        print(f"缺失值总数: {df.isnull().sum().sum()}")
        
        # 数值型变量统计
        numeric_data = df.select_dtypes(include=[np.number])
        if len(numeric_data.columns) > 0:
            print(f"\n数值型变量描述性统计:")
            print(numeric_data.describe())
        
        # 分类变量统计
        categorical_data = df.select_dtypes(include=['object'])
        if len(categorical_data.columns) > 0:
            print(f"\n分类变量:")
            for col in categorical_data.columns[:5]:  # 只显示前5个
                print(f"{col}: {df[col].nunique()} 个唯一值")
        
        print("✓ 基本分析完成")
        
    except Exception as e:
        print(f"✗ 基本分析时出错: {e}")

def main():
    """主测试函数"""
    print("慢性肾病数据分析测试")
    print("=" * 50)
    
    # 1. 测试数据加载
    loaded_data = test_data_loading()
    
    if not loaded_data:
        print("没有成功加载任何数据文件！")
        return
    
    # 2. 对每个成功加载的数据集进行测试
    for data_name, df in loaded_data.items():
        print(f"\n{'='*20} 分析 {data_name} {'='*20}")
        
        # 基本分析
        test_basic_analysis(df, data_name)
        
        # 可视化测试
        test_data_visualization(df, data_name)
        
        print(f"{'='*50}")
    
    print("\n测试完成！")
    print("如果所有测试都通过，说明数据和代码准备就绪。")
    print("现在可以运行完整的分析：python run_analysis.py")

if __name__ == "__main__":
    main()
