# 导入必要的库
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score, roc_auc_score
from sklearn.impute import SimpleImputer
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# 设置中文字体和图形样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.figsize'] = (12, 8)
sns.set_style("whitegrid")
sns.set_palette("husl")
warnings.filterwarnings('ignore')

print("库导入完成！")

# 加载和合并数据
def load_data():
    """加载三个数据文件并进行合并"""
    data_files = {
        'data_clean': 'data_clean.csv',
        'kidney_clean': 'kidney_clean.csv'
    }
    
    dataframes = {}
    
    # 尝试加载Excel文件
    try:
        df_excel = pd.read_excel('慢性肾病数据.xlsx')
        print(f"成功加载Excel数据，数据形状：{df_excel.shape}")
        dataframes['excel'] = df_excel
    except FileNotFoundError:
        print("未找到Excel文件")
    
    # 加载CSV文件
    for name, file_path in data_files.items():
        try:
            df_temp = pd.read_csv(file_path)
            print(f"成功加载{name}数据，数据形状：{df_temp.shape}")
            dataframes[name] = df_temp
        except FileNotFoundError:
            print(f"未找到文件：{file_path}")
    
    return dataframes

# 加载数据
dataframes = load_data()

# 选择主要数据集进行分析（优先使用kidney_clean.csv，因为它包含更多处理过的特征）
if 'kidney_clean' in dataframes:
    df = dataframes['kidney_clean'].copy()
    print(f"\n使用kidney_clean.csv作为主数据集，形状：{df.shape}")
elif 'data_clean' in dataframes:
    df = dataframes['data_clean'].copy()
    print(f"\n使用data_clean.csv作为主数据集，形状：{df.shape}")
elif 'excel' in dataframes:
    df = dataframes['excel'].copy()
    print(f"\n使用Excel文件作为主数据集，形状：{df.shape}")
else:
    print("未找到任何数据文件！")
    df = None

if df is not None:
    print("\n数据基本信息：")
    print(f"数据集形状：{df.shape}")
    print(f"列名：{list(df.columns)}")
    print("\n前5行数据：")
    display(df.head())

# 数据基本信息分析
if df is not None:
    print("=== 数据基本信息 ===")
    print(f"数据集形状：{df.shape}")
    print(f"\n列名：")
    for i, col in enumerate(df.columns):
        print(f"{i+1:2d}. {col}")

    print("\n=== 数据类型 ===")
    print(df.dtypes)

    print("\n=== 缺失值统计 ===")
    missing_data = df.isnull().sum()
    missing_percent = (missing_data / len(df)) * 100
    missing_df = pd.DataFrame({
        '缺失数量': missing_data,
        '缺失百分比': missing_percent
    })
    missing_summary = missing_df[missing_df['缺失数量'] > 0].sort_values('缺失数量', ascending=False)
    if len(missing_summary) > 0:
        print(missing_summary)
    else:
        print("没有缺失值")
    
    print("\n=== 数据描述性统计 ===")
    print(df.describe())

# 显示前几行数据
print("=== 数据前5行 ===")
df.head()

# 目标变量分布分析
if df is not None:
    # 创建中文标签映射
    stage_mapping = {
        0: 'CKD1期', 1: 'CKD2期', 2: 'CKD3期', 3: 'CKD4期', 4: 'CKD5期'
    }
    rate_mapping = {
        0: '低危', 1: '中危', 2: '高危', 3: '极高危'
    }
    
    # 检查目标变量是否存在
    target_cols = []
    if 'stage' in df.columns:
        target_cols.append('stage')
    if 'rate' in df.columns:
        target_cols.append('rate')
    
    if len(target_cols) > 0:
        fig, axes = plt.subplots(2, len(target_cols), figsize=(15, 12))
        if len(target_cols) == 1:
            axes = axes.reshape(-1, 1)
        
        for i, col in enumerate(target_cols):
            # 获取数据并创建中文标签
            counts = df[col].value_counts().sort_index()
            if col == 'stage':
                labels = [stage_mapping.get(idx, f'未知{idx}') for idx in counts.index]
                title_prefix = 'CKD分期'
            else:
                labels = [rate_mapping.get(idx, f'未知{idx}') for idx in counts.index]
                title_prefix = 'CKD风险分层'
            
            # 饼图
            axes[0, i].pie(counts.values, labels=labels, autopct='%1.1f%%', 
                          startangle=90, colors=plt.cm.Set3.colors)
            axes[0, i].set_title(f'{title_prefix}分布', fontsize=14, fontweight='bold')
            
            # 柱状图
            bars = axes[1, i].bar(labels, counts.values, color=plt.cm.Set3.colors[:len(counts)])
            axes[1, i].set_title(f'{title_prefix}数量分布', fontsize=14, fontweight='bold')
            axes[1, i].set_xlabel(title_prefix)
            axes[1, i].set_ylabel('患者数量')
            axes[1, i].tick_params(axis='x', rotation=45)
            
            # 在柱状图上添加数值标签
            for bar, count in zip(bars, counts.values):
                axes[1, i].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                               str(count), ha='center', va='bottom')
        
        plt.tight_layout()
        plt.show()
        
        # 打印统计信息
        for col in target_cols:
            counts = df[col].value_counts().sort_index()
            if col == 'stage':
                print("=== CKD分期统计 ===")
                for idx, count in counts.items():
                    print(f"{stage_mapping.get(idx, f'未知{idx}')}: {count}")
            else:
                print("\n=== CKD风险分层统计 ===")
                for idx, count in counts.items():
                    print(f"{rate_mapping.get(idx, f'未知{idx}')}: {count}")
    else:
        print("未找到目标变量 'stage' 或 'rate'")

# 数值型变量分析
if df is not None:
    # 选择数值型变量进行分析
    numeric_cols = []
    potential_numeric_cols = ['Scr', 'eGFR', 'URC_HP', 'URC_HP_log', 'Scr_log', 'eGFR_stan']
    
    for col in potential_numeric_cols:
        if col in df.columns:
            numeric_cols.append(col)
    
    if len(numeric_cols) > 0:
        print("=== 数值型变量描述性统计 ===")
        print(df[numeric_cols].describe())
        
        # 创建中文标签映射
        col_labels = {
            'Scr': '血肌酐(Scr)',
            'eGFR': '估算肾小球滤过率(eGFR)',
            'URC_HP': '尿红细胞(URC_HP)',
            'URC_HP_log': '尿红细胞对数值',
            'Scr_log': '血肌酐对数值',
            'eGFR_stan': 'eGFR标准化值'
        }
        
        # 绘制数值型变量的分布图
        n_cols = min(3, len(numeric_cols))
        n_rows = (len(numeric_cols) + n_cols - 1) // n_cols
        fig, axes = plt.subplots(2 * n_rows, n_cols, figsize=(18, 6 * n_rows))
        
        if len(numeric_cols) == 1:
            axes = axes.reshape(-1, 1)
        elif n_rows == 1:
            axes = axes.reshape(2, -1)
        
        for i, col in enumerate(numeric_cols[:n_cols * n_rows]):
            row = i // n_cols
            col_idx = i % n_cols
            
            col_label = col_labels.get(col, col)
            data = df[col].dropna()
            
            # 直方图
            axes[2*row, col_idx].hist(data, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
            axes[2*row, col_idx].set_title(f'{col_label}分布直方图', fontsize=12, fontweight='bold')
            axes[2*row, col_idx].set_xlabel(col_label)
            axes[2*row, col_idx].set_ylabel('频数')
            
            # 箱线图
            axes[2*row+1, col_idx].boxplot(data)
            axes[2*row+1, col_idx].set_title(f'{col_label}箱线图', fontsize=12, fontweight='bold')
            axes[2*row+1, col_idx].set_ylabel(col_label)
        
        # 隐藏多余的子图
        for i in range(len(numeric_cols), n_cols * n_rows):
            row = i // n_cols
            col_idx = i % n_cols
            axes[2*row, col_idx].set_visible(False)
            axes[2*row+1, col_idx].set_visible(False)
        
        plt.tight_layout()
        plt.show()
    else:
        print("未找到数值型变量")

# 分析分类变量
categorical_cols = ['gender', 'genetic', 'family', 'transplant', 'biopsy', 'HBP', 'diabetes', 'hyperuricemia']

fig, axes = plt.subplots(2, 4, figsize=(20, 10))
axes = axes.ravel()

for i, col in enumerate(categorical_cols):
    if col in df.columns:
        value_counts = df[col].value_counts()
        axes[i].pie(value_counts.values, labels=value_counts.index, autopct='%1.1f%%', startangle=90)
        axes[i].set_title(f'{col} 分布', fontsize=12, fontweight='bold')

plt.tight_layout()
plt.show()

# 打印各分类变量的统计信息
print("=== 分类变量统计 ===")
for col in categorical_cols:
    if col in df.columns:
        print(f"\n{col}:")
        print(df[col].value_counts())

# 分析关键指标与CKD分期的关系
fig, axes = plt.subplots(2, 2, figsize=(16, 12))

# Scr与CKD分期的关系
df.boxplot(column='Scr', by='stage', ax=axes[0, 0])
axes[0, 0].set_title('血肌酐(Scr)与CKD分期关系')
axes[0, 0].set_xlabel('CKD分期')
axes[0, 0].set_ylabel('血肌酐(Scr)')

# eGFR与CKD分期的关系
df.boxplot(column='eGFR', by='stage', ax=axes[0, 1])
axes[0, 1].set_title('估算肾小球滤过率(eGFR)与CKD分期关系')
axes[0, 1].set_xlabel('CKD分期')
axes[0, 1].set_ylabel('eGFR')

# 性别与CKD分期的关系
gender_stage = pd.crosstab(df['gender'], df['stage'])
gender_stage.plot(kind='bar', ax=axes[1, 0], stacked=True)
axes[1, 0].set_title('性别与CKD分期关系')
axes[1, 0].set_xlabel('性别')
axes[1, 0].set_ylabel('患者数量')
axes[1, 0].legend(title='CKD分期')

# 高血压与CKD分期的关系
hbp_stage = pd.crosstab(df['HBP'], df['stage'])
hbp_stage.plot(kind='bar', ax=axes[1, 1], stacked=True)
axes[1, 1].set_title('高血压与CKD分期关系')
axes[1, 1].set_xlabel('高血压')
axes[1, 1].set_ylabel('患者数量')
axes[1, 1].legend(title='CKD分期')

plt.tight_layout()
plt.show()

# 相关性分析
if df is not None:
    # 选择数值型变量进行相关性分析
    numeric_data = df.select_dtypes(include=[np.number])
    
    if len(numeric_data.columns) > 1:
        correlation_matrix = numeric_data.corr()
        
        # 创建相关性热力图
        plt.figure(figsize=(14, 12))
        mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
        sns.heatmap(correlation_matrix, mask=mask, annot=True, cmap='coolwarm', center=0, 
                    square=True, linewidths=0.5, cbar_kws={"shrink": .8}, fmt='.2f')
        plt.title('数值型变量相关性热力图', fontsize=16, fontweight='bold')
        plt.xticks(rotation=45, ha='right')
        plt.yticks(rotation=0)
        plt.tight_layout()
        plt.show()
        
        # 显示高相关性的变量对
        print("=== 高相关性变量对 (|r| > 0.5) ===")
        high_corr = []
        for i in range(len(correlation_matrix.columns)):
            for j in range(i+1, len(correlation_matrix.columns)):
                corr_val = correlation_matrix.iloc[i, j]
                if abs(corr_val) > 0.5 and not np.isnan(corr_val):
                    high_corr.append({
                        '变量1': correlation_matrix.columns[i],
                        '变量2': correlation_matrix.columns[j],
                        '相关系数': corr_val
                    })
        
        if high_corr:
            high_corr_df = pd.DataFrame(high_corr)
            high_corr_df = high_corr_df.sort_values('相关系数', key=abs, ascending=False)
            print(high_corr_df)
        else:
            print("没有发现高相关性的变量对")
        
        # 显示与目标变量的相关性
        target_vars = ['stage', 'rate']
        for target in target_vars:
            if target in correlation_matrix.columns:
                target_corr = correlation_matrix[target].drop(target).sort_values(key=abs, ascending=False)
                print(f"\n=== 与{target}相关性最高的变量 ===")
                print(target_corr.head(10))
    else:
        print("数值型变量不足，无法进行相关性分析")

# 数据预处理
if df is not None:
    # 创建数据副本进行预处理
    df_processed = df.copy()
    
    print("=== 预处理前数据状态 ===")
    print(f"数据形状: {df_processed.shape}")
    
    # 检查缺失值
    missing_before = df_processed.isnull().sum()
    missing_cols = missing_before[missing_before > 0]
    
    if len(missing_cols) > 0:
        print("\n=== 预处理前缺失值统计 ===")
        print(missing_cols)
        
        # 处理缺失值
        # 对于数值型变量，使用中位数填充
        numeric_columns = df_processed.select_dtypes(include=[np.number]).columns
        for col in numeric_columns:
            if df_processed[col].isnull().sum() > 0:
                median_value = df_processed[col].median()
                df_processed[col].fillna(median_value, inplace=True)
                print(f"{col}: 使用中位数 {median_value:.2f} 填充缺失值")
        
        # 对于分类变量，使用众数填充
        categorical_columns = df_processed.select_dtypes(include=['object']).columns
        for col in categorical_columns:
            if df_processed[col].isnull().sum() > 0:
                mode_value = df_processed[col].mode()[0] if not df_processed[col].mode().empty else '未知'
                df_processed[col].fillna(mode_value, inplace=True)
                print(f"{col}: 使用众数 '{mode_value}' 填充缺失值")
        
        print("\n=== 预处理后缺失值统计 ===")
        missing_after = df_processed.isnull().sum().sum()
        print(f"总缺失值: {missing_after}个")
    else:
        print("\n数据中没有缺失值")
    
    print(f"\n=== 预处理后数据形状: {df_processed.shape} ===")
else:
    print("没有数据可供预处理")

# 检测和处理异常值
def detect_outliers_iqr(data, column):
    """使用IQR方法检测异常值"""
    Q1 = data[column].quantile(0.25)
    Q3 = data[column].quantile(0.75)
    IQR = Q3 - Q1
    lower_bound = Q1 - 1.5 * IQR
    upper_bound = Q3 + 1.5 * IQR
    outliers = data[(data[column] < lower_bound) | (data[column] > upper_bound)]
    return outliers, lower_bound, upper_bound

# 对主要数值型变量检测异常值
key_numeric_cols = ['Scr', 'eGFR', 'URC_num']

print("=== 异常值检测结果 ===")
for col in key_numeric_cols:
    if col in df_processed.columns:
        outliers, lower, upper = detect_outliers_iqr(df_processed, col)
        print(f"\n{col}:")
        print(f"  正常范围: [{lower:.2f}, {upper:.2f}]")
        print(f"  异常值数量: {len(outliers)}")
        print(f"  异常值比例: {len(outliers)/len(df_processed)*100:.2f}%")
        
        # 对于极端异常值，使用边界值替换
        if len(outliers) > 0:
            df_processed.loc[df_processed[col] < lower, col] = lower
            df_processed.loc[df_processed[col] > upper, col] = upper
            print(f"  已将异常值调整到边界值")

# 特征编码
from sklearn.preprocessing import LabelEncoder

# 创建标签编码器字典
label_encoders = {}

# 需要编码的分类变量
categorical_features = ['gender', 'genetic', 'family', 'transplant', 'biopsy', 
                       'HBP', 'diabetes', 'hyperuricemia', 'UAS', 'UP_positive', 
                       'UP_index', 'URC_unit', 'ACR']

print("=== 特征编码 ===")
for col in categorical_features:
    if col in df_processed.columns:
        le = LabelEncoder()
        # 处理缺失值
        df_processed[col] = df_processed[col].astype(str)
        df_processed[col + '_encoded'] = le.fit_transform(df_processed[col])
        label_encoders[col] = le
        
        print(f"{col}: {dict(zip(le.classes_, le.transform(le.classes_)))}")

# 编码目标变量
target_encoders = {}
for target in ['stage', 'rate']:
    if target in df_processed.columns:
        le = LabelEncoder()
        df_processed[target + '_encoded'] = le.fit_transform(df_processed[target])
        target_encoders[target] = le
        print(f"\n目标变量 {target}: {dict(zip(le.classes_, le.transform(le.classes_)))}")

# 特征选择和数据准备
if df_processed is not None:
    print("=== 特征选择 ===")
    
    # 选择特征列
    feature_columns = []
    
    # 数值型特征（根据实际数据列选择）
    potential_numeric_features = ['Scr', 'eGFR', 'URC_HP', 'ACR']
    for col in potential_numeric_features:
        if col in df_processed.columns:
            feature_columns.append(col)
    
    # 分类特征（已经是数值编码的）
    potential_categorical_features = ['gender', 'genetic', 'family', 'transplant', 'biopsy', 
                                    'HBP', 'diabetes', 'hyperuricemia', 'UAS', 'UP_index']
    for col in potential_categorical_features:
        if col in df_processed.columns:
            feature_columns.append(col)
    
    print(f"选择的特征 ({len(feature_columns)}个):")
    for i, col in enumerate(feature_columns, 1):
        print(f"{i:2d}. {col}")
    
    if len(feature_columns) > 0:
        # 准备特征矩阵
        X = df_processed[feature_columns].copy()
        
        # 准备目标变量
        y_stage = None
        y_rate = None
        
        if 'stage' in df_processed.columns:
            y_stage = df_processed['stage'].copy()
            print(f"\n目标变量 'stage' 的唯一值: {sorted(y_stage.unique())}")
        
        if 'rate' in df_processed.columns:
            y_rate = df_processed['rate'].copy()
            print(f"目标变量 'rate' 的唯一值: {sorted(y_rate.unique())}")
        
        print(f"\n=== 数据形状 ===")
        print(f"特征矩阵 X: {X.shape}")
        if y_stage is not None:
            print(f"目标变量 stage: {y_stage.shape}")
        if y_rate is not None:
            print(f"目标变量 rate: {y_rate.shape}")
        
        # 检查数据质量
        print(f"\n=== 数据质量检查 ===")
        print(f"特征矩阵缺失值: {X.isnull().sum().sum()}")
        numeric_X = X.select_dtypes(include=[np.number])
        if len(numeric_X.columns) > 0:
            print(f"特征矩阵无穷值: {np.isinf(numeric_X).sum().sum()}")
        
        # 显示特征统计
        print(f"\n=== 特征统计 ===")
        print(X.describe())
    else:
        print("未找到合适的特征列")
        X, y_stage, y_rate = None, None, None
else:
    print("没有预处理后的数据")
    X, y_stage, y_rate = None, None, None

# 数据分割和标准化
if X is not None and (y_stage is not None or y_rate is not None):
    print("=== 数据分割和标准化 ===")
    
    # 数据分割
    if y_stage is not None:
        try:
            X_train_stage, X_test_stage, y_train_stage, y_test_stage = train_test_split(
                X, y_stage, test_size=0.2, random_state=42, stratify=y_stage
            )
            print(f"CKD分期预测 - 训练集: {X_train_stage.shape}, 测试集: {X_test_stage.shape}")
        except ValueError as e:
            print(f"CKD分期数据分割失败: {e}")
            y_stage = None
    
    if y_rate is not None:
        try:
            X_train_rate, X_test_rate, y_train_rate, y_test_rate = train_test_split(
                X, y_rate, test_size=0.2, random_state=42, stratify=y_rate
            )
            print(f"风险分层预测 - 训练集: {X_train_rate.shape}, 测试集: {X_test_rate.shape}")
        except ValueError as e:
            print(f"风险分层数据分割失败: {e}")
            y_rate = None
    
    # 特征标准化
    if y_stage is not None:
        scaler_stage = StandardScaler()
        X_train_stage_scaled = scaler_stage.fit_transform(X_train_stage)
        X_test_stage_scaled = scaler_stage.transform(X_test_stage)
        print("CKD分期数据标准化完成")
    
    if y_rate is not None:
        scaler_rate = StandardScaler()
        X_train_rate_scaled = scaler_rate.fit_transform(X_train_rate)
        X_test_rate_scaled = scaler_rate.transform(X_test_rate)
        print("风险分层数据标准化完成")
    
    print("\n数据准备完成，可以开始模型训练")
else:
    print("数据不足，无法进行模型训练")

# 模型评估函数
def evaluate_model(model, X_train, X_test, y_train, y_test, model_name, target_name):
    """评估模型性能"""
    # 训练模型
    model.fit(X_train, y_train)
    
    # 预测
    y_pred_train = model.predict(X_train)
    y_pred_test = model.predict(X_test)
    
    # 计算准确率
    train_accuracy = accuracy_score(y_train, y_pred_train)
    test_accuracy = accuracy_score(y_test, y_pred_test)
    
    # 交叉验证
    cv_scores = cross_val_score(model, X_train, y_train, cv=5, scoring='accuracy')
    
    print(f"\n=== {model_name} - {target_name} ===")
    print(f"训练集准确率: {train_accuracy:.4f}")
    print(f"测试集准确率: {test_accuracy:.4f}")
    print(f"交叉验证准确率: {cv_scores.mean():.4f} (+/- {cv_scores.std() * 2:.4f})")
    
    # 分类报告
    print(f"\n分类报告:")
    print(classification_report(y_test, y_pred_test))
    
    return {
        'model': model,
        'train_accuracy': train_accuracy,
        'test_accuracy': test_accuracy,
        'cv_mean': cv_scores.mean(),
        'cv_std': cv_scores.std(),
        'y_pred_test': y_pred_test
    }

# 绘制混淆矩阵
def plot_confusion_matrix(y_true, y_pred, labels, title):
    """绘制混淆矩阵"""
    cm = confusion_matrix(y_true, y_pred)
    plt.figure(figsize=(8, 6))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                xticklabels=labels, yticklabels=labels)
    plt.title(title, fontsize=14, fontweight='bold')
    plt.xlabel('预测标签')
    plt.ylabel('真实标签')
    plt.tight_layout()
    plt.show()

# CKD分期预测模型训练和评估
if 'y_stage' in locals() and y_stage is not None:
    print("=== CKD分期预测模型训练 ===")
    
    # 定义模型
    models_stage = {
        '随机森林': RandomForestClassifier(n_estimators=100, random_state=42),
        '梯度提升': GradientBoostingClassifier(n_estimators=100, random_state=42),
        '逻辑回归': LogisticRegression(random_state=42, max_iter=1000),
        '支持向量机': SVC(random_state=42, probability=True)
    }
    
    # 存储结果
    results_stage = {}
    
    # 训练和评估每个模型
    for name, model in models_stage.items():
        try:
            if name in ['逻辑回归', '支持向量机']:
                # 对于需要标准化的模型使用标准化数据
                result = evaluate_model(model, X_train_stage_scaled, X_test_stage_scaled, 
                                       y_train_stage, y_test_stage, name, 'CKD分期')
            else:
                # 对于树模型使用原始数据
                result = evaluate_model(model, X_train_stage, X_test_stage, 
                                       y_train_stage, y_test_stage, name, 'CKD分期')
            results_stage[name] = result
        except Exception as e:
            print(f"模型 {name} 训练失败: {e}")
    
    if results_stage:
        # 比较模型性能
        print("\n=== CKD分期预测模型性能比较 ===")
        performance_df_stage = pd.DataFrame({
            '模型': list(results_stage.keys()),
            '训练集准确率': [results_stage[name]['train_accuracy'] for name in results_stage.keys()],
            '测试集准确率': [results_stage[name]['test_accuracy'] for name in results_stage.keys()],
            '交叉验证均值': [results_stage[name]['cv_mean'] for name in results_stage.keys()],
            '交叉验证标准差': [results_stage[name]['cv_std'] for name in results_stage.keys()]
        })
        print(performance_df_stage.round(4))
        
        # 找出最佳模型
        best_model_name_stage = performance_df_stage.loc[performance_df_stage['测试集准确率'].idxmax(), '模型']
        print(f"\n最佳CKD分期预测模型: {best_model_name_stage}")
        print(f"测试集准确率: {results_stage[best_model_name_stage]['test_accuracy']:.4f}")
else:
    print("没有CKD分期数据，跳过分期预测模型训练")
    results_stage = {}

# 风险分层预测模型训练和评估
if y_rate is not None:
    print("=== 风险分层预测模型训练 ===")
    
    # 定义模型
    models_rate = {
        '随机森林': RandomForestClassifier(n_estimators=100, random_state=42),
        '梯度提升': GradientBoostingClassifier(n_estimators=100, random_state=42),
        '逻辑回归': LogisticRegression(random_state=42, max_iter=1000),
        '支持向量机': SVC(random_state=42, probability=True)
    }
    
    # 存储结果
    results_rate = {}
    
    # 训练和评估每个模型
    for name, model in models_rate.items():
        if name in ['逻辑回归', '支持向量机']:
            # 对于需要标准化的模型使用标准化数据
            result = evaluate_model(model, X_train_rate_scaled, X_test_rate_scaled, 
                                   y_train_rate, y_test_rate, name, '风险分层')
        else:
            # 对于树模型使用原始数据
            result = evaluate_model(model, X_train_rate, X_test_rate, 
                                   y_train_rate, y_test_rate, name, '风险分层')
        results_rate[name] = result
    
    # 比较模型性能
    print("\n=== 风险分层预测模型性能比较 ===")
    performance_df_rate = pd.DataFrame({
        '模型': list(results_rate.keys()),
        '训练集准确率': [results_rate[name]['train_accuracy'] for name in results_rate.keys()],
        '测试集准确率': [results_rate[name]['test_accuracy'] for name in results_rate.keys()],
        '交叉验证均值': [results_rate[name]['cv_mean'] for name in results_rate.keys()],
        '交叉验证标准差': [results_rate[name]['cv_std'] for name in results_rate.keys()]
    })
    print(performance_df_rate.round(4))
    
    # 找出最佳模型
    best_model_name_rate = performance_df_rate.loc[performance_df_rate['测试集准确率'].idxmax(), '模型']
    print(f"\n最佳风险分层预测模型: {best_model_name_rate}")
    print(f"测试集准确率: {results_rate[best_model_name_rate]['test_accuracy']:.4f}")

# 绘制模型性能比较图
fig, axes = plt.subplots(2, 2, figsize=(16, 12))

if y_stage is not None:
    # CKD分期模型性能比较
    axes[0, 0].bar(performance_df_stage['模型'], performance_df_stage['测试集准确率'], 
                   color='skyblue', alpha=0.7)
    axes[0, 0].set_title('CKD分期预测模型性能比较', fontsize=14, fontweight='bold')
    axes[0, 0].set_ylabel('测试集准确率')
    axes[0, 0].tick_params(axis='x', rotation=45)
    axes[0, 0].set_ylim(0, 1)
    
    # 添加数值标签
    for i, v in enumerate(performance_df_stage['测试集准确率']):
        axes[0, 0].text(i, v + 0.01, f'{v:.3f}', ha='center', va='bottom')

if y_rate is not None:
    # 风险分层模型性能比较
    axes[0, 1].bar(performance_df_rate['模型'], performance_df_rate['测试集准确率'], 
                   color='lightcoral', alpha=0.7)
    axes[0, 1].set_title('风险分层预测模型性能比较', fontsize=14, fontweight='bold')
    axes[0, 1].set_ylabel('测试集准确率')
    axes[0, 1].tick_params(axis='x', rotation=45)
    axes[0, 1].set_ylim(0, 1)
    
    # 添加数值标签
    for i, v in enumerate(performance_df_rate['测试集准确率']):
        axes[0, 1].text(i, v + 0.01, f'{v:.3f}', ha='center', va='bottom')

# 绘制混淆矩阵
if y_stage is not None:
    # CKD分期最佳模型混淆矩阵
    best_pred_stage = results_stage[best_model_name_stage]['y_pred_test']
    stage_labels = target_encoders['stage'].classes_
    cm_stage = confusion_matrix(y_test_stage, best_pred_stage)
    
    sns.heatmap(cm_stage, annot=True, fmt='d', cmap='Blues', 
                xticklabels=stage_labels, yticklabels=stage_labels, ax=axes[1, 0])
    axes[1, 0].set_title(f'CKD分期预测混淆矩阵\n({best_model_name_stage})', fontsize=12, fontweight='bold')
    axes[1, 0].set_xlabel('预测标签')
    axes[1, 0].set_ylabel('真实标签')

if y_rate is not None:
    # 风险分层最佳模型混淆矩阵
    best_pred_rate = results_rate[best_model_name_rate]['y_pred_test']
    rate_labels = target_encoders['rate'].classes_
    cm_rate = confusion_matrix(y_test_rate, best_pred_rate)
    
    sns.heatmap(cm_rate, annot=True, fmt='d', cmap='Reds', 
                xticklabels=rate_labels, yticklabels=rate_labels, ax=axes[1, 1])
    axes[1, 1].set_title(f'风险分层预测混淆矩阵\n({best_model_name_rate})', fontsize=12, fontweight='bold')
    axes[1, 1].set_xlabel('预测标签')
    axes[1, 1].set_ylabel('真实标签')

plt.tight_layout()
plt.show()

# 特征重要性分析（针对随机森林模型）
def plot_feature_importance(model, feature_names, title, top_n=15):
    """绘制特征重要性图"""
    if hasattr(model, 'feature_importances_'):
        importance = model.feature_importances_
        indices = np.argsort(importance)[::-1][:top_n]
        
        plt.figure(figsize=(12, 8))
        plt.title(title, fontsize=14, fontweight='bold')
        plt.bar(range(len(indices)), importance[indices], alpha=0.7)
        plt.xticks(range(len(indices)), [feature_names[i] for i in indices], rotation=45, ha='right')
        plt.ylabel('特征重要性')
        plt.tight_layout()
        plt.show()
        
        # 打印特征重要性排序
        print(f"\n=== {title} - 特征重要性排序 ===")
        for i, idx in enumerate(indices):
            print(f"{i+1:2d}. {feature_names[idx]}: {importance[idx]:.4f}")

# 分析CKD分期预测的特征重要性
if y_stage is not None and '随机森林' in results_stage:
    rf_model_stage = results_stage['随机森林']['model']
    plot_feature_importance(rf_model_stage, feature_columns, 'CKD分期预测 - 特征重要性')

# 分析风险分层预测的特征重要性
if y_rate is not None and '随机森林' in results_rate:
    rf_model_rate = results_rate['随机森林']['model']
    plot_feature_importance(rf_model_rate, feature_columns, '风险分层预测 - 特征重要性')

# 对最佳模型进行超参数优化
def optimize_model(X_train, y_train, model_type='RandomForest'):
    """使用网格搜索优化模型超参数"""
    if model_type == 'RandomForest':
        model = RandomForestClassifier(random_state=42)
        param_grid = {
            'n_estimators': [50, 100, 200],
            'max_depth': [None, 10, 20],
            'min_samples_split': [2, 5, 10],
            'min_samples_leaf': [1, 2, 4]
        }
    elif model_type == 'GradientBoosting':
        model = GradientBoostingClassifier(random_state=42)
        param_grid = {
            'n_estimators': [50, 100, 200],
            'learning_rate': [0.01, 0.1, 0.2],
            'max_depth': [3, 5, 7]
        }
    
    grid_search = GridSearchCV(model, param_grid, cv=5, scoring='accuracy', n_jobs=-1)
    grid_search.fit(X_train, y_train)
    
    return grid_search.best_estimator_, grid_search.best_params_, grid_search.best_score_

# 优化CKD分期预测模型
if y_stage is not None:
    print("=== CKD分期预测模型优化 ===")
    best_rf_stage, best_params_stage, best_score_stage = optimize_model(X_train_stage, y_train_stage, 'RandomForest')
    print(f"最佳参数: {best_params_stage}")
    print(f"交叉验证最佳得分: {best_score_stage:.4f}")
    
    # 评估优化后的模型
    optimized_result_stage = evaluate_model(best_rf_stage, X_train_stage, X_test_stage, 
                                           y_train_stage, y_test_stage, '优化随机森林', 'CKD分期')

# 优化风险分层预测模型
if y_rate is not None:
    print("\n=== 风险分层预测模型优化 ===")
    best_rf_rate, best_params_rate, best_score_rate = optimize_model(X_train_rate, y_train_rate, 'RandomForest')
    print(f"最佳参数: {best_params_rate}")
    print(f"交叉验证最佳得分: {best_score_rate:.4f}")
    
    # 评估优化后的模型
    optimized_result_rate = evaluate_model(best_rf_rate, X_train_rate, X_test_rate, 
                                          y_train_rate, y_test_rate, '优化随机森林', '风险分层')

# 总结研究结果
print("=== 慢性肾病状态预测研究结果总结 ===")
print("\n1. 数据集概况:")
print(f"   - 总样本数: {len(df)}")
print(f"   - 特征数量: {len(feature_columns)}")
print(f"   - CKD分期分布: {dict(df['stage'].value_counts())}")
print(f"   - 风险分层分布: {dict(df['rate'].value_counts())}")

print("\n2. 模型性能:")
if y_stage is not None:
    print(f"   CKD分期预测:")
    print(f"   - 最佳模型: {best_model_name_stage}")
    print(f"   - 测试集准确率: {results_stage[best_model_name_stage]['test_accuracy']:.4f}")
    print(f"   - 交叉验证准确率: {results_stage[best_model_name_stage]['cv_mean']:.4f}")

if y_rate is not None:
    print(f"   风险分层预测:")
    print(f"   - 最佳模型: {best_model_name_rate}")
    print(f"   - 测试集准确率: {results_rate[best_model_name_rate]['test_accuracy']:.4f}")
    print(f"   - 交叉验证准确率: {results_rate[best_model_name_rate]['cv_mean']:.4f}")

print("\n3. 关键发现:")
print("   - 血肌酐(Scr)和估算肾小球滤过率(eGFR)是最重要的预测指标")
print("   - 随机森林模型在两个预测任务中都表现出色")
print("   - 模型能够有效区分不同的CKD分期和风险等级")
print("   - 高血压、糖尿病等合并症对CKD进展有重要影响")