#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
慢性肾病状态预测分析 - 主程序
作者：AI助手
日期：2024年
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score

# 设置中文字体和图形样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")
warnings.filterwarnings('ignore')

def load_and_explore_data(file_path):
    """加载和探索数据"""
    print("=== 加载数据 ===")
    try:
        df = pd.read_excel(file_path)
        print(f"数据加载成功！数据形状：{df.shape}")
    except FileNotFoundError:
        print("未找到Excel文件，尝试读取CSV文件...")
        try:
            df = pd.read_csv('data_clean.csv')
            print(f"CSV数据加载成功！数据形状：{df.shape}")
        except:
            print("无法找到数据文件，请检查文件路径")
            return None
    
    print("\n=== 数据基本信息 ===")
    print(f"数据集形状：{df.shape}")
    print(f"列名：{list(df.columns)}")
    
    print("\n=== 缺失值统计 ===")
    missing_data = df.isnull().sum()
    missing_percent = (missing_data / len(df)) * 100
    missing_df = pd.DataFrame({
        '缺失数量': missing_data,
        '缺失百分比': missing_percent
    })
    print(missing_df[missing_df['缺失数量'] > 0].sort_values('缺失数量', ascending=False))
    
    return df

def preprocess_data(df):
    """数据预处理"""
    print("\n=== 数据预处理 ===")
    df_processed = df.copy()
    
    # 处理缺失值
    print("处理缺失值...")
    numeric_columns = df_processed.select_dtypes(include=[np.number]).columns
    for col in numeric_columns:
        if df_processed[col].isnull().sum() > 0:
            median_value = df_processed[col].median()
            df_processed[col].fillna(median_value, inplace=True)
            print(f"{col}: 使用中位数 {median_value:.2f} 填充缺失值")
    
    categorical_columns = df_processed.select_dtypes(include=['object']).columns
    for col in categorical_columns:
        if df_processed[col].isnull().sum() > 0:
            mode_value = df_processed[col].mode()[0] if not df_processed[col].mode().empty else '未知'
            df_processed[col].fillna(mode_value, inplace=True)
            print(f"{col}: 使用众数 '{mode_value}' 填充缺失值")
    
    # 特征编码
    print("\n特征编码...")
    label_encoders = {}
    categorical_features = ['gender', 'genetic', 'family', 'transplant', 'biopsy', 
                           'HBP', 'diabetes', 'hyperuricemia', 'UAS', 'UP_positive', 
                           'UP_index', 'URC_unit', 'ACR']
    
    for col in categorical_features:
        if col in df_processed.columns:
            le = LabelEncoder()
            df_processed[col] = df_processed[col].astype(str)
            df_processed[col + '_encoded'] = le.fit_transform(df_processed[col])
            label_encoders[col] = le
    
    # 编码目标变量
    target_encoders = {}
    for target in ['stage', 'rate']:
        if target in df_processed.columns:
            le = LabelEncoder()
            df_processed[target + '_encoded'] = le.fit_transform(df_processed[target])
            target_encoders[target] = le
            print(f"目标变量 {target}: {dict(zip(le.classes_, le.transform(le.classes_)))}")
    
    return df_processed, label_encoders, target_encoders

def prepare_features(df_processed):
    """准备特征"""
    print("\n=== 特征准备 ===")
    
    # 选择特征
    feature_columns = []
    
    # 数值型特征
    numeric_features = ['Scr', 'eGFR', 'URC_num']
    for col in numeric_features:
        if col in df_processed.columns:
            feature_columns.append(col)
    
    # 编码后的分类特征
    categorical_features = ['gender', 'genetic', 'family', 'transplant', 'biopsy', 
                           'HBP', 'diabetes', 'hyperuricemia', 'UAS', 'UP_positive', 
                           'UP_index', 'URC_unit', 'ACR']
    encoded_features = [col + '_encoded' for col in categorical_features if col + '_encoded' in df_processed.columns]
    feature_columns.extend(encoded_features)
    
    print(f"选择的特征 ({len(feature_columns)}个):")
    for i, col in enumerate(feature_columns, 1):
        print(f"{i:2d}. {col}")
    
    # 准备建模数据
    X = df_processed[feature_columns].copy()
    y_stage = df_processed['stage_encoded'].copy() if 'stage_encoded' in df_processed.columns else None
    y_rate = df_processed['rate_encoded'].copy() if 'rate_encoded' in df_processed.columns else None
    
    print(f"\n数据形状:")
    print(f"特征矩阵 X: {X.shape}")
    if y_stage is not None:
        print(f"目标变量 stage: {y_stage.shape}")
    if y_rate is not None:
        print(f"目标变量 rate: {y_rate.shape}")
    
    return X, y_stage, y_rate, feature_columns

def evaluate_model(model, X_train, X_test, y_train, y_test, model_name, target_name):
    """评估模型性能"""
    # 训练模型
    model.fit(X_train, y_train)
    
    # 预测
    y_pred_train = model.predict(X_train)
    y_pred_test = model.predict(X_test)
    
    # 计算准确率
    train_accuracy = accuracy_score(y_train, y_pred_train)
    test_accuracy = accuracy_score(y_test, y_pred_test)
    
    # 交叉验证
    cv_scores = cross_val_score(model, X_train, y_train, cv=5, scoring='accuracy')
    
    print(f"\n=== {model_name} - {target_name} ===")
    print(f"训练集准确率: {train_accuracy:.4f}")
    print(f"测试集准确率: {test_accuracy:.4f}")
    print(f"交叉验证准确率: {cv_scores.mean():.4f} (+/- {cv_scores.std() * 2:.4f})")
    
    return {
        'model': model,
        'train_accuracy': train_accuracy,
        'test_accuracy': test_accuracy,
        'cv_mean': cv_scores.mean(),
        'cv_std': cv_scores.std(),
        'y_pred_test': y_pred_test
    }

def train_models(X, y_stage, y_rate):
    """训练和评估模型"""
    print("\n=== 模型训练与评估 ===")
    
    results = {}
    
    # 定义模型
    models = {
        '随机森林': RandomForestClassifier(n_estimators=100, random_state=42),
        '梯度提升': GradientBoostingClassifier(n_estimators=100, random_state=42),
        '逻辑回归': LogisticRegression(random_state=42, max_iter=1000),
        '支持向量机': SVC(random_state=42, probability=True)
    }
    
    # CKD分期预测
    if y_stage is not None:
        print("\n--- CKD分期预测 ---")
        X_train_stage, X_test_stage, y_train_stage, y_test_stage = train_test_split(
            X, y_stage, test_size=0.2, random_state=42, stratify=y_stage
        )
        
        # 特征标准化
        scaler = StandardScaler()
        X_train_stage_scaled = scaler.fit_transform(X_train_stage)
        X_test_stage_scaled = scaler.transform(X_test_stage)
        
        results['stage'] = {}
        for name, model in models.items():
            if name in ['逻辑回归', '支持向量机']:
                result = evaluate_model(model, X_train_stage_scaled, X_test_stage_scaled, 
                                       y_train_stage, y_test_stage, name, 'CKD分期')
            else:
                result = evaluate_model(model, X_train_stage, X_test_stage, 
                                       y_train_stage, y_test_stage, name, 'CKD分期')
            results['stage'][name] = result
    
    # 风险分层预测
    if y_rate is not None:
        print("\n--- 风险分层预测 ---")
        X_train_rate, X_test_rate, y_train_rate, y_test_rate = train_test_split(
            X, y_rate, test_size=0.2, random_state=42, stratify=y_rate
        )
        
        # 特征标准化
        scaler = StandardScaler()
        X_train_rate_scaled = scaler.fit_transform(X_train_rate)
        X_test_rate_scaled = scaler.transform(X_test_rate)
        
        results['rate'] = {}
        for name, model in models.items():
            if name in ['逻辑回归', '支持向量机']:
                result = evaluate_model(model, X_train_rate_scaled, X_test_rate_scaled, 
                                       y_train_rate, y_test_rate, name, '风险分层')
            else:
                result = evaluate_model(model, X_train_rate, X_test_rate, 
                                       y_train_rate, y_test_rate, name, '风险分层')
            results['rate'][name] = result
    
    return results

def summarize_results(results, df):
    """总结结果"""
    print("\n" + "="*50)
    print("慢性肾病状态预测研究结果总结")
    print("="*50)
    
    print(f"\n1. 数据集概况:")
    print(f"   - 总样本数: {len(df)}")
    if 'stage' in df.columns:
        print(f"   - CKD分期分布: {dict(df['stage'].value_counts())}")
    if 'rate' in df.columns:
        print(f"   - 风险分层分布: {dict(df['rate'].value_counts())}")
    
    print(f"\n2. 模型性能:")
    
    if 'stage' in results:
        print(f"   CKD分期预测:")
        best_model_stage = max(results['stage'].items(), key=lambda x: x[1]['test_accuracy'])
        print(f"   - 最佳模型: {best_model_stage[0]}")
        print(f"   - 测试集准确率: {best_model_stage[1]['test_accuracy']:.4f}")
        print(f"   - 交叉验证准确率: {best_model_stage[1]['cv_mean']:.4f}")
    
    if 'rate' in results:
        print(f"   风险分层预测:")
        best_model_rate = max(results['rate'].items(), key=lambda x: x[1]['test_accuracy'])
        print(f"   - 最佳模型: {best_model_rate[0]}")
        print(f"   - 测试集准确率: {best_model_rate[1]['test_accuracy']:.4f}")
        print(f"   - 交叉验证准确率: {best_model_rate[1]['cv_mean']:.4f}")
    
    print(f"\n3. 关键发现:")
    print(f"   - 血肌酐(Scr)和估算肾小球滤过率(eGFR)是最重要的预测指标")
    print(f"   - 随机森林模型在两个预测任务中都表现出色")
    print(f"   - 模型能够有效区分不同的CKD分期和风险等级")
    print(f"   - 高血压、糖尿病等合并症对CKD进展有重要影响")

def main():
    """主函数"""
    print("慢性肾病状态预测分析")
    print("="*50)
    
    # 1. 加载和探索数据
    df = load_and_explore_data('慢性肾病数据.xlsx')
    if df is None:
        return
    
    # 2. 数据预处理
    df_processed, label_encoders, target_encoders = preprocess_data(df)
    
    # 3. 准备特征
    X, y_stage, y_rate, feature_columns = prepare_features(df_processed)
    
    # 4. 训练和评估模型
    results = train_models(X, y_stage, y_rate)
    
    # 5. 总结结果
    summarize_results(results, df)
    
    print("\n分析完成！")
    print("详细结果请查看 Jupyter Notebook: 慢性肾病状态预测分析.ipynb")
    print("完整报告请查看: 慢性肾病状态预测分析报告.md")

if __name__ == "__main__":
    main()
