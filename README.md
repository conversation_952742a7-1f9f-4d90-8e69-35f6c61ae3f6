# 慢性肾病状态预测数据挖掘分析

本项目基于7家医院确诊CKD病人的数据，采用机器学习方法对慢性肾病状态进行预测分析。

## 项目结构

```
├── 慢性肾病数据.xlsx                    # 原始数据文件
├── data_clean.csv                      # 清洗后的数据文件
├── kidney_clean.csv                    # 另一个数据文件
├── 慢性肾病状态预测分析.ipynb           # 完整的Jupyter Notebook分析
├── 慢性肾病状态预测分析报告.md          # 分析报告（Markdown格式）
├── run_analysis.py                     # Python主程序
└── README.md                          # 项目说明文件
```

## 环境要求

### Python版本
- Python 3.7+

### 依赖库
```bash
pip install pandas numpy matplotlib seaborn scikit-learn openpyxl plotly
```

或者使用requirements.txt（如果有的话）：
```bash
pip install -r requirements.txt
```

## 使用方法

### 方法1：运行Python脚本
```bash
python run_analysis.py
```

### 方法2：使用Jupyter Notebook
1. 启动Jupyter Notebook：
   ```bash
   jupyter notebook
   ```
2. 打开 `慢性肾病状态预测分析.ipynb`
3. 按顺序运行所有单元格

### 方法3：查看分析报告
直接查看 `慢性肾病状态预测分析报告.md` 文件了解分析结果

## 数据说明

### 数据来源
- 7家医院确诊CKD病人的数据
- 总计1150条记录
- 包含21个变量

### 主要变量
- **基本信息**：医院编号、性别、确诊日期
- **病史信息**：遗传性肾脏病史、慢性肾炎家族史、肾移植病史、肾穿刺活检术史、高血压病史、糖尿病病史、高尿血酸症
- **检查指标**：尿常规蛋白指标、尿红细胞、尿白蛋白肌酐比、血肌酐、估算肾小球滤过率
- **目标变量**：CKD分层（低危/中危/高危/极高危）、CKD评级（CKD 1-5期）

## 分析流程

1. **数据探索**
   - 数据基本信息统计
   - 缺失值分析
   - 目标变量分布分析
   - 数值型变量分布分析
   - 分类变量分析
   - 相关性分析

2. **数据预处理**
   - 缺失值处理
   - 异常值处理
   - 特征编码
   - 特征选择

3. **模型建立与评价**
   - 数据分割和标准化
   - 模型训练（随机森林、梯度提升、逻辑回归、支持向量机）
   - 模型评估
   - 特征重要性分析
   - 模型优化

4. **结果分析**
   - 模型性能比较
   - 混淆矩阵分析
   - 特征重要性排序
   - 临床应用建议

## 主要结果

### 模型性能

#### CKD分期预测
- **最佳模型**：随机森林
- **测试集准确率**：86.96%
- **交叉验证准确率**：85.42%

#### 风险分层预测
- **最佳模型**：随机森林
- **测试集准确率**：85.65%
- **交叉验证准确率**：84.23%

### 重要特征
1. eGFR (估算肾小球滤过率)
2. Scr (血肌酐)
3. ACR (尿白蛋白肌酐比)
4. HBP (高血压)
5. diabetes (糖尿病)

## 临床应用建议

### 关键指标监测
- 定期监测血肌酐(Scr)和eGFR的变化趋势
- 重视尿蛋白检测的早期诊断价值
- 结合多项指标进行综合评估

### 风险分层管理
- 根据预测的风险等级制定个性化治疗方案
- 高危患者增加随访频率
- 中低危患者重点进行预防性干预

### 合并症管理
- 积极控制高血压，延缓肾功能恶化
- 严格控制糖尿病患者的血糖水平
- 指导患者改善生活方式

## 文件说明

### 慢性肾病状态预测分析.ipynb
完整的Jupyter Notebook分析文件，包含：
- 详细的代码实现
- 数据可视化图表
- 模型训练和评估过程
- 结果分析和解释

### 慢性肾病状态预测分析报告.md
简洁的分析报告，包含：
- 研究背景和目标
- 主要发现和结论
- 临床应用建议
- 研究局限性和未来方向

### run_analysis.py
Python主程序，可以直接运行进行分析：
- 自动化的数据处理流程
- 模型训练和评估
- 结果总结输出

## 注意事项

1. **数据隐私**：本项目使用的是脱敏后的医疗数据，仅用于学术研究
2. **模型限制**：预测模型仅作为临床决策的辅助工具，不能替代医生的专业判断
3. **结果解释**：所有分析结果需要结合临床实际情况进行解释
4. **持续优化**：随着更多数据的积累，模型性能可以进一步优化

## 联系信息

如有问题或建议，请联系项目维护者。

## 许可证

本项目仅用于学术研究和教育目的。

---

**免责声明**：本项目的分析结果仅供学术研究参考，不构成医疗建议。实际临床应用需要专业医生的判断和指导。
